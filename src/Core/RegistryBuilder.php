<?php

namespace Flexiwind\Core;
use Symfony\Component\Console\Output\OutputInterface;

class RegistryBuilder
{
    public function build(string $schemaPath, string $outputDir='r'): void
    {
        if (!file_exists($schemaPath)) {
            throw new \RuntimeException("Schema file not found: $schemaPath");
        }

        $schema = json_decode(file_get_contents($schemaPath), true);
        if (!$schema || !isset($schema['components'])) {
            throw new \RuntimeException("Invalid schema file");
        }

        if (!is_dir(getcwd().'/'.$outputDir)) {
            mkdir(getcwd().'/'.$outputDir, 0777, true);
        }

        foreach ($schema['components'] as $component) {
            $files = [];

            foreach ($component['files'] as $fileItem) {
                $filePath = $fileItem['path'];

                if (!file_exists($filePath)) {
                    throw new \RuntimeException("File not found: $filePath");
                }

                $files[] = [
                    'path'    => $filePath,
                    'type'    => $fileItem['type'] ?? 'registry:component',
                    'target'  => $fileItem['target'] ?? $filePath,
                    'content' => file_get_contents($filePath),
                ];

            }

            

            $registry = [
                'name'                => $component['name'],
                'title'               => $component['title'] ?? '',
                'description'         => $component['description'] ?? '',
                'registryDependencies'=> $component['registryDependencies'] ?? [],
                'files'               => $files,
            ];

            $outputFile = rtrim(getcwd().'/'.$outputDir, '/') . '/' . $component['name'] . '.json';
            file_put_contents(
                $outputFile,
                json_encode($registry, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE)
            );
        }
    }
}
