{"name": "button", "title": "Primary Button", "description": "A styled button component for Laravel Blade.", "registryDependencies": ["input"], "files": [{"path": "components/button.blade.php", "type": "registry:component", "target": "resources/views/components/button.blade.php", "content": "@props([\n    'variant' => 'solid',\n    'intent' => 'primary',\n    'size' => 'md',\n    'disabled' => false,\n    'type' => 'button',\n    'radius' => 'lg',\n    'href' => null,\n])\n\n@php\n    use App\\Flexiwind\\ButtonHelper;\n    use App\\Flexiwind\\UiHelper;\n    $btn_variants = ButtonHelper::getVariants();\n\n    $radius_class = UiHelper::getRadius($radius);\n    $sizes = [\n        'xs' => 'btn-xs',\n        'sm' => 'btn-sm',\n        'md' => 'btn-md',\n        'lg' => 'btn-lg',\n        'xl' => 'btn-xl',\n        'icon-xs' => 'btn-icon-xs',\n        'icon-sm' => 'btn-icon-sm',\n        'icon-md' => 'btn-icon-md',\n        'icon-lg' => 'btn-icon-lg',\n        'icon-xl' => 'btn-icon-xl',\n        'none' => '',\n    ];\n    $baseClasses = 'btn ease-linear duration-200 ';\n    if ($variant !== 'none') {\n        $baseClasses .= isset($btn_variants[$variant]) ? $btn_variants[$variant]['base'] : 'ee';\n    }\n\n    $variantClasses = '';\n    if ($variant !== 'none') {\n        if (isset($btn_variants[$variant]) && isset($btn_variants[$variant]['intents'][$intent])) {\n            $variantClasses = $btn_variants[$variant]['intents'][$intent];\n        } elseif (isset($btn_variants['solid']['intents'][$intent])) {\n            $variantClasses = $btn_variants['solid']['intents'][$intent];\n        }\n    }\n\n    $sizeClasses = isset($sizes[$size]) ? $sizes[$size] : $sizes['md'];\n\n    $tag = $href ? 'a' : 'button';\n\n    $attributes = $attributes->class([\n        $baseClasses,\n        $variantClasses,\n        $sizeClasses,\n        $radius_class,\n        'cursor-not-allowed' => $disabled,\n    ]);\n\n    $isInternal = true;\n    if ($href) {\n        $isInternal = ($href && Str::startsWith($href, '/')) || Str::startsWith($href, '#');\n    }\n\n    $attributes =\n        $tag === 'button'\n            ? ($attributes = $attributes->merge([\n                'type' => $type,\n                'disabled' => $disabled,\n            ]))\n            : $attributes->merge([\n                'href' => $disabled ? null : $href,\n                'aria-disabled' => $disabled ? 'true' : null,\n                'tabindex' => $disabled ? '-1' : null,\n            ]);\n\n@endphp\n\n<{{ $tag }} {{ $attributes }}\n    @if (!$isInternal) target=\"_blank\"  rel=\"noopener noreferrer\" @endif>\n    {{ $slot }}\n    </{{ $tag }}>\n"}, {"path": "css/button.css", "type": "registry:ui", "target": "css/button.css", "content": "@custom-variant dark (&:is(.dark *));\n:root{\n    --primary: var(--color-primary-600);\n    --secondary: var(--color-secondary-600);\n    --accent: var(--color-accent-600);\n    --info: var(--color-info-600);\n    --warning: var(--color-warning-600);\n    --danger: var(--color-danger-600);\n    --success: var(--color-success-600);\n\n\n    /* background colors  */\n    --bg: var(--color-white); \n    --bg-subtle: var(--color-gray-50); \n    --bg-surface: var(--color-gray-100); \n    --bg-muted: var(--color-gray-200); \n    --bg-surface-elevated: var(--color-gray-300); \n\n\n    /* foreground colors  */\n    --fg: var(--color-gray-700); \n    --fg-muted: var(--color-gray-600); \n    --fg-subtitle: var(--color-gray-800); \n    --fg-title: var(--color-gray-900); \n\n\n    /* border colors  */\n    --border: var(--color-gray-200); \n    --border-subtle: var(--color-gray-100); \n    --border-strong: var(--color-gray-300); \n    --border-amphasis: var(--color-gray-400); \n    --border-input: var(--color-gray-200);\n\n\n    /* card  */\n    --card: var(--color-white); \n    --card-gray: var(--color-gray-100); \n\n\n    /* popover : For Dropdowns & popovers  */\n    --popover: var(--color-white); \n    --popover-gray: var(--color-gray-100);\n}\n.dark{\n    --primary: var(--color-primary-500);\n    --secondary: var(--color-secondary-500);\n    --accent: var(--color-accent-500);\n    --info: var(--color-info-500);\n    --warning: var(--color-warning-500);\n    --danger: var(--color-danger-500);\n    --success: var(--color-success-500);\n\n\n    /* background colors  */\n    --bg: var(--color-gray-950); \n    --bg-subtle: var(--color-900); \n    --bg-surface: var(--color-950); \n    --bg-muted: var(--color-800); \n    --bg-surface-elevated: var(--color-700); \n\n\n    /* foreground colors  */\n    --fg: var(--color-gray-300); \n    --fg-muted: var(--color-gray-400); \n    --fg-subtitle: var(--color-gray-100); \n    --fg-title: var(--color-white); \n\n\n    /* border colors  */\n    --border: var(--color-gray-900); \n    --border-subtle: var(--color-gray-950); \n    --border-strong: var(--color-gray-800); \n    --border-amphasis: var(--color-gray-700); \n    --border-input: var(--color-gray-900);\n\n\n    /* card  */\n    --card: var(--color-gray-950); \n    --card-gray: var(--color-gray-900); \n\n\n    /* popover : For Dropdowns & popovers  */\n    --popover: var(--color-gray-950); \n    --popover-gray: var(--color-gray-900);\n}\n\n@theme inline {\n    --color-primary: var(--primary);\n    --color-secondary: var(--secondary);\n    --color-accent: var(--accent);\n    --color-info: var(--info);\n    --color-warning: var(--warning);\n    --color-danger: var(--danger);\n    --color-success: var(--success);\n\n\n    /* background colors  */\n    --color-bg: var(--bg); \n    --color-bg-subtle: var(--bg-subtle); \n    --color-bg-surface: var(--bg-surface); \n    --color-bg-muted: var(--bg-muted); \n    --color-bg-surface-elevated: var(--bg-surface-elevated); \n\n\n    /* foreground colors  */\n    --color-fg: var(--fg); \n    --color-fg-muted: var(--fg-muted); \n    --color-fg-subtitle: var(--fg-subtitle); \n    --color-fg-title: var(--fg-title); \n\n\n    /* border colors  */\n    --color-border: var(--border); \n    --color-border-subtle: var(--border-subtle); \n    --color-border-strong: var(--border-strong); \n    --color-border-amphasis: var(--border-amphasis); \n    --color-border-input: var(--border-input);\n\n\n    /* card  */\n    --color-card: var(--card-bg); \n    --color-card-gray: var(--card-gray); \n\n\n    /* popover : For Dropdowns & popovers  */\n    --color-popover: var(--popover-bg); \n    --color-popover-gray: var(--popover-gray);\n\n\n\n    /* define primary colors */\n    --color-primary-50: hsl(252, 100%, 97%);\n    --color-primary-100: hsl(252, 100%, 94%);\n    --color-primary-200: hsl(251, 98%, 89%);\n    --color-primary-300: hsl(249, 95%, 82%);\n    --color-primary-400: hsl(247, 90%, 74%);\n    --color-primary-500: hsl(245, 86%, 67%);\n    --color-primary-600: hsl(243, 75%, 59%);\n    --color-primary-700: hsl(244, 57%, 51%);\n    --color-primary-800: hsl(245, 54%, 41%);\n    --color-primary-900: hsl(245, 47%, 35%);\n    --color-primary-950: hsl(246, 47%, 20%);\n\n    --color-secondary-50: hsl(183, 100%, 96%);\n    --color-secondary-100: hsl(185, 96%, 90%);\n    --color-secondary-200: hsl(186, 93%, 82%);\n    --color-secondary-300: hsl(187, 92%, 69%);\n    --color-secondary-400: hsl(188, 86%, 53%);\n    --color-secondary-500: hsl(187, 97%, 43%);\n    --color-secondary-600: hsl(187, 92%, 36%);\n    --color-secondary-700: hsl(188, 78%, 31%);\n    --color-secondary-800: hsl(201, 90%, 27%);\n    --color-secondary-900: hsl(190, 56%, 24%);\n    --color-secondary-950: hsl(192, 79%, 15%);\n\n    /* other colors if needed */\n}    "}]}