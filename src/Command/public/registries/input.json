{"name": "input", "title": "Text Input", "description": "Reusable input field with styles.", "registryDependencies": [], "files": [{"path": "components/input.blade.php", "type": "registry:component", "target": "resources/views/components/input.blade.php", "content": "@props([\n    'type' => 'text',\n    'size' => 'md',\n    'disabled' => false,\n    'readonly' => false,\n    'label' => null,\n    'noBorder' => false,\n    'inlinedLabel' => false,\n    'labelClass' => '',\n    'radius' => 'lg',\n    'unStylled' => false,\n])\n\n@php\n    $sizes = [\n        'none' => '',\n        'sm' => 'ui-form-input-sm',\n        'md' => 'ui-form-input-md',\n        'lg' => 'ui-form-input-lg',\n    ];\n    $input_radius = [\n        'none' => '',\n        'sm' => 'rounded-sm',\n        'md' => 'rounded-md',\n        'lg' => 'rounded-lg',\n        '3xl' => 'rounded-3xl',\n        'full' => 'rounded-full',\n    ];\n    $radius_ = $input_radius[$radius] ?? $input_radius['lg'];\n\n    $border_class = $noBorder ? 'border-transparent' : 'border border-border-input shadow bg-bg';\n    $baseClasses = $unStylled ? '' : \"ui-form-base ui-form-input {$radius_} {$border_class} text-fg\";\n    $sizeClasses = $sizes[$size] ?? $sizes['md'];\n\n    $id = $attributes->get('id') ?? $attributes->get('name', uniqid('input-'));\n\n    $attributes = $attributes->class([$baseClasses, $sizeClasses])->merge([\n        'type' => $type,\n        'disabled' => $disabled,\n        'readonly' => $readonly,\n        'id' => $id,\n    ]);\n\n    $group_wrapper = $inlinedLabel ? 'flex items-center gap-2' : 'flex flex-col space-y-2';\n@endphp\n\n@if ($label)\n    <div class=\"{{ $group_wrapper }}\">\n        <x-ui.label for=\"{{ $id }}\" :text=\"$label\" class=\"{{ $labelClass }}\" />\n        <input {{ $attributes }}>\n    </div>\n@else\n    <input {{ $attributes }}>\n@endif\n"}]}