<?php

namespace Flexiwind\Command;

use <PERSON>lexiwind\Core\RegistryBuilder;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class BuildCommand extends Command
{
    protected static $defaultName = 'build';

    protected function configure()
    {
        $this->setName('build')
            ->setDescription('Build registries from flexiwind.schema.json')
            ->addOption(
                'output',
                'o',
                InputOption::VALUE_OPTIONAL,
                'Output directory name (relative to current directory)',
                'public/r'
            );
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $builder = new RegistryBuilder();

        // Get the output directory from option or use default
        $outputDir = $input->getOption('output');

        // Build the full output path: current working directory + output folder
        $fullOutputPath = getcwd() . '/' . ltrim($outputDir, '/');

        try {
            $builder->build('flexiwind.schema.json', $fullOutputPath);
            $output->writeln("<info>Registries built successfully in: {$fullOutputPath}</info>");
        } catch (\Exception $e) {
            $output->writeln('<error>' . $e->getMessage() . '</error>');
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
