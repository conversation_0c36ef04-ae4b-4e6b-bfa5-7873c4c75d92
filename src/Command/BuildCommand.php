<?php

namespace Flexiwind\Command;

use <PERSON>lexi<PERSON>\Core\RegistryBuilder;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class BuildCommand extends Command
{
    protected static $defaultName = 'build';

    protected function configure()
    {
        $this->setName('build')
            ->setDescription('Build registries from flexiwind.schema.json');
    }

    // InputInterface $input, OutputInterface $output
    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $builder = new RegistryBuilder();

        try {
            $builder->build('flexiwind.schema.json', __DIR__ . '/public/registries',$output);
            $output->writeln('<info>Registries built successfully!</info>');
        } catch (\Exception $e) {
            $output->writeln('<error>' . $e->getMessage() . '</error>');
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
