@utility ui-solid-gray {
    --ui-solid-bg: var(--color-gray-100);
    --ui-solid-text: var(--color-gray-700);
}
@utility ui-solid-success {
    --ui-solid-bg: var(--color-success-600);
    --ui-solid-text: var(--color-white);
}

@utility ui-solid-warning {
    --ui-solid-bg: var(--color-warning-600);
    --ui-solid-text: var(--color-white);
}

@utility ui-solid-danger {
    --ui-solid-bg: var(--color-danger-600);
    --ui-solid-text: var(--color-white);
}

@utility ui-solid-neutral {
    --ui-solid-bg: var(--color-gray-900);
    --ui-solid-text: var(--color-white);
}

@utility ui-outline-success {
    --ui-outline-border: var(--color-success-600);
    --ui-outline-text: var(--color-success-600);
}

@utility ui-outline-warning {
    --ui-outline-border: var(--color-warning-600);
    --ui-outline-text: var(--color-warning-600);
}

@utility ui-outline-danger {
    --ui-outline-border: var(--color-danger-600);
    --ui-outline-text: var(--color-danger-600);
}

@utility ui-soft-success {
    --ui-soft-bg: --alpha(var(--color-success-100) / 40%);
    --ui-soft-text: var(--color-success-600);
}

@utility ui-soft-warning {
    --ui-soft-bg: --alpha(var(--color-warning-100) / 40%);
    --ui-soft-text: var(--color-warning-600);
}

@utility ui-soft-danger {
    --ui-soft-bg: --alpha(var(--color-danger-100) / 40%);
    --ui-soft-text: var(--color-danger-600);
}

@utility ui-subtle-success {
    --ui-subtle-bg: --alpha(var(--color-success-100) / 40%);
    --ui-subtle-text: var(--color-success-600);
    --ui-subtle-border: var(--color-success-300);
}

@utility ui-subtle-warning {
    --ui-subtle-bg: --alpha(var(--color-warning-100) / 40%);
    --ui-subtle-text: var(--color-warning-600);
    --ui-subtle-border: var(--color-warning-300);
}

@utility ui-subtle-danger {
    --ui-subtle-bg: --alpha(var(--color-danger-100) / 40%);
    --ui-subtle-text: var(--color-danger-600);
    --ui-subtle-border: var(--color-danger-300);
}
